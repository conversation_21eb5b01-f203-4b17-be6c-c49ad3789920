/* ===================================
   CleanSpace Solutions - Main Stylesheet
   Modern Cleaning Service Template
   =================================== */

/* Import Google Font - Poppins */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

/* ===================================
   CSS RESET & BASE STYLES
   =================================== */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #ffffff;
}

/* ===================================
   COLOR VARIABLES
   =================================== */

:root {
    --primary-blue: #2563eb;
    --light-blue: #dbeafe;
    --dark-blue: #1e40af;
    --success-green: #10b981;
    --dark-green: #059669;
    --text-dark: #1f2937;
    --text-light: #6b7280;
    --white: #ffffff;
    --light-gray: #f9fafb;
    --border-gray: #e5e7eb;
}

/* ===================================
   TYPOGRAPHY
   =================================== */

h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

h1 {
    font-size: 3rem;
    font-weight: 700;
}

h2 {
    font-size: 2.5rem;
}

h3 {
    font-size: 2rem;
}

h4 {
    font-size: 1.5rem;
}

p {
    color: var(--text-light);
    margin-bottom: 1rem;
}

/* ===================================
   UTILITY CLASSES
   =================================== */

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.text-center {
    text-align: center;
}

.btn {
    display: inline-block;
    padding: 14px 35px;
    border: none;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 1rem;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-blue), var(--dark-blue));
    color: var(--white);
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--dark-blue), var(--primary-blue));
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-green), var(--dark-green));
    color: var(--white);
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, var(--dark-green), var(--success-green));
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.section-padding {
    padding: 80px 0;
}

/* ===================================
   HEADER & NAVIGATION
   =================================== */

header {
    background-color: var(--white);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 0;
    min-height: 80px;
}

.logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-blue);
    text-decoration: none;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-links a {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    font-size: 1rem;
    padding: 0.5rem 1rem;
    transition: color 0.3s ease;
}

.nav-links a:hover {
    color: var(--primary-blue);
}

/* Mobile Menu Toggle */
.menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.menu-toggle span {
    width: 25px;
    height: 3px;
    background-color: var(--text-dark);
    transition: all 0.3s ease;
}

/* ===================================
   HERO SECTION
   =================================== */

.hero {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--dark-blue) 100%);
    color: var(--white);
    padding: 150px 0 100px;
    text-align: center;
    position: relative;
    overflow: hidden;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('../images/hero-cleaning.jpg') center/cover;
    opacity: 0.15;
    z-index: 1;
}

/* Cool animated background particles */
.hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 2px, transparent 2px),
        radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 2px, transparent 2px),
        radial-gradient(circle at 40% 40%, rgba(255,255,255,0.05) 1px, transparent 1px);
    background-size: 100px 100px, 150px 150px, 80px 80px;
    animation: float 20s ease-in-out infinite;
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero h1 {
    color: var(--white);
    margin-bottom: 1.5rem;
    animation: fadeInUp 1s ease;
}

.hero p {
    font-size: 1.2rem;
    color: rgba(255,255,255,0.9);
    margin-bottom: 2rem;
    animation: fadeInUp 1s ease 0.2s both;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease 0.4s both;
}

/* ===================================
   PAGE HERO SECTION (for other pages)
   =================================== */

.page-hero {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #06b6d4 100%);
    color: var(--white);
    padding: 140px 0 100px;
    text-align: center;
    position: relative;
    overflow: hidden;
    min-height: 60vh;
    display: flex;
    align-items: center;
}

.page-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('../images/hero-cleaning.jpg') center/cover;
    opacity: 0.15;
    z-index: 1;
}

.page-hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(255,255,255,0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(255,255,255,0.05) 0%, transparent 50%);
    animation: float 15s ease-in-out infinite;
    z-index: 2;
    pointer-events: none;
}

.page-hero-content {
    position: relative;
    z-index: 3;
    max-width: 800px;
    margin: 0 auto;
}

.page-hero h1 {
    color: var(--white);
    margin-bottom: 1.5rem;
    font-size: 3.2rem;
    font-weight: 700;
    text-shadow: 0 4px 20px rgba(0,0,0,0.3);
    animation: slideInDown 1s ease;
}

.page-hero p {
    font-size: 1.3rem;
    color: rgba(255,255,255,0.95);
    margin-bottom: 2rem;
    text-shadow: 0 2px 10px rgba(0,0,0,0.2);
    animation: slideInUp 1s ease 0.2s both;
}

.page-hero-breadcrumb {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-top: 2rem;
    font-size: 1rem;
    opacity: 0.9;
    animation: fadeIn 1s ease 0.4s both;
}

.page-hero-breadcrumb a {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.page-hero-breadcrumb a:hover {
    color: var(--white);
}

.page-hero-breadcrumb span {
    color: rgba(255,255,255,0.6);
}

/* ===================================
   ANIMATIONS
   =================================== */

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-10px) rotate(1deg);
    }
    66% {
        transform: translateY(5px) rotate(-1deg);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes sweep {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }
    100% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* ===================================
   SERVICES SECTION
   =================================== */

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.service-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(37, 99, 235, 0.1), transparent);
    transition: left 0.6s ease;
}

.service-card:hover::before {
    left: 100%;
}

.service-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 15px 35px rgba(37, 99, 235, 0.2);
    border-color: var(--primary-blue);
}

.service-icon {
    font-size: 3rem;
    color: var(--primary-blue);
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    display: inline-block;
}

.service-card:hover .service-icon {
    animation: bounce 0.6s ease;
    color: var(--success-green);
}

/* ===================================
   TESTIMONIALS SECTION
   =================================== */

.testimonials {
    background-color: var(--light-gray);
}

.testimonial-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.testimonial-card {
    background: var(--white);
    padding: 2.5rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    position: relative;
    transition: all 0.3s ease;
    border-left: 4px solid var(--success-green);
}

.testimonial-card::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: 20px;
    font-size: 4rem;
    color: var(--primary-blue);
    opacity: 0.3;
    font-family: serif;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.testimonial-text {
    font-style: italic;
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    line-height: 1.6;
    color: var(--text-dark);
}

.testimonial-author {
    font-weight: 600;
    color: var(--primary-blue);
    position: relative;
    padding-left: 1rem;
}

.testimonial-author::before {
    content: '—';
    position: absolute;
    left: 0;
    color: var(--success-green);
}

/* ===================================
   FOOTER
   =================================== */

footer {
    background-color: var(--text-dark);
    color: var(--white);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    color: var(--white);
    margin-bottom: 1rem;
}

.footer-section p,
.footer-section a {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
}

.footer-section a:hover {
    color: var(--white);
}

.social-icons {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-icons a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background-color: var(--primary-blue);
    color: var(--white);
    text-align: center;
    line-height: 40px;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.social-icons a:hover {
    background-color: var(--success-green);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255,255,255,0.1);
    color: rgba(255,255,255,0.6);
}

/* ===================================
   SCROLL TO TOP BUTTON
   =================================== */

.scroll-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background: linear-gradient(135deg, var(--success-green), var(--dark-green));
    color: var(--white);
    width: 55px;
    height: 55px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 999;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
    font-size: 1.2rem;
}

.scroll-top.show {
    opacity: 1;
    visibility: visible;
    animation: pulse 2s infinite;
}

.scroll-top:hover {
    background: linear-gradient(135deg, var(--dark-green), var(--success-green));
    transform: translateY(-5px) scale(1.1);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

/* ===================================
   LOADING SCREEN
   =================================== */

.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-blue), var(--dark-blue));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 1;
    visibility: visible;
    transition: all 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid var(--white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===================================
   FORMS
   =================================== */

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-dark);
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid var(--border-gray);
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-blue);
}

textarea.form-control {
    resize: vertical;
    min-height: 120px;
}

/* ===================================
   FAQ ACCORDION
   =================================== */

.faq-item {
    background: var(--white);
    border: 1px solid var(--border-gray);
    border-radius: 5px;
    margin-bottom: 1rem;
    overflow: hidden;
}

.faq-question {
    background: var(--light-gray);
    padding: 1.5rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.faq-question:hover {
    background-color: var(--light-blue);
}

.faq-question.active {
    background-color: var(--primary-blue);
    color: var(--white);
}

.faq-answer {
    padding: 0 1.5rem;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-answer.active {
    padding: 1.5rem;
    max-height: 500px;
}

.faq-icon {
    transition: transform 0.3s ease;
}

.faq-question.active .faq-icon {
    transform: rotate(180deg);
}

/* ===================================
   BOOKING FORM STYLES
   =================================== */

.booking-form {
    background: var(--white);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    max-width: 600px;
    margin: 0 auto;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

/* ===================================
   ABOUT PAGE STYLES
   =================================== */

/* Enhanced Story Section */
.story-content {
    max-width: 900px;
    margin: 3rem auto;
    position: relative;
}

.story-card {
    background: linear-gradient(135deg, var(--white) 0%, rgba(37, 99, 235, 0.02) 100%);
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: 1px solid rgba(37, 99, 235, 0.1);
    position: relative;
    overflow: hidden;
}

.story-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, var(--primary-blue), var(--success-green));
}

.story-text {
    font-size: 1.2rem;
    line-height: 1.8;
    color: var(--text-dark);
    margin-bottom: 2rem;
}

.story-highlight {
    background: linear-gradient(120deg, rgba(37, 99, 235, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    font-weight: 600;
}

/* Enhanced Values Section */
.values-intro {
    max-width: 700px;
    margin: 0 auto 3rem;
    text-align: center;
}

.feature-item {
    background: var(--white);
    padding: 2.5rem 2rem;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
}

.feature-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(37, 99, 235, 0.05), transparent);
    transition: left 0.6s ease;
}

.feature-item:hover::before {
    left: 100%;
}

.feature-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(37, 99, 235, 0.15);
    border-color: var(--primary-blue);
}

.feature-icon {
    width: 90px;
    height: 90px;
    background: linear-gradient(135deg, var(--primary-blue), var(--dark-blue));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2.5rem;
    color: var(--white);
    transition: all 0.3s ease;
    box-shadow: 0 8px 20px rgba(37, 99, 235, 0.3);
}

.feature-item:hover .feature-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 12px 30px rgba(37, 99, 235, 0.4);
}

/* Enhanced Team Section */
.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2.5rem;
    margin-top: 3rem;
}

.team-member {
    text-align: center;
    background: var(--white);
    padding: 2.5rem 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
}

.team-member::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--success-green));
}

.team-member:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    border-color: var(--primary-blue);
}

.team-photo {
    width: 160px;
    height: 160px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--light-blue), var(--primary-blue));
    margin: 0 auto 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3.5rem;
    color: var(--white);
    transition: all 0.3s ease;
    box-shadow: 0 10px 25px rgba(37, 99, 235, 0.2);
    position: relative;
}

.team-photo::before {
    content: '';
    position: absolute;
    inset: -3px;
    background: linear-gradient(45deg, var(--primary-blue), var(--success-green), var(--primary-blue));
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.team-member:hover .team-photo::before {
    opacity: 1;
}

.team-member:hover .team-photo {
    transform: scale(1.05);
    box-shadow: 0 15px 35px rgba(37, 99, 235, 0.3);
}

.team-role {
    color: var(--primary-blue);
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.team-bio {
    color: var(--text-light);
    line-height: 1.6;
}

/* Timeline Section */
.timeline {
    position: relative;
    max-width: 800px;
    margin: 3rem auto;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(180deg, var(--primary-blue), var(--success-green));
    transform: translateX(-50%);
}

.timeline-item {
    position: relative;
    margin: 3rem 0;
    display: flex;
    align-items: center;
}

.timeline-item:nth-child(odd) {
    flex-direction: row;
}

.timeline-item:nth-child(even) {
    flex-direction: row-reverse;
}

.timeline-content {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    width: 45%;
    position: relative;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.timeline-content:hover {
    border-color: var(--primary-blue);
    transform: scale(1.02);
    box-shadow: 0 12px 35px rgba(37, 99, 235, 0.15);
}

.timeline-year {
    background: linear-gradient(135deg, var(--primary-blue), var(--dark-blue));
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    display: inline-block;
}

.timeline-dot {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    background: var(--success-green);
    border-radius: 50%;
    border: 4px solid var(--white);
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
    z-index: 2;
}

/* Achievements Section */
.achievements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.achievement-item {
    text-align: center;
    padding: 2rem 1rem;
    background: var(--white);
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.achievement-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--success-green), var(--primary-blue));
}

.achievement-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.achievement-icon {
    font-size: 2.5rem;
    color: var(--success-green);
    margin-bottom: 1rem;
}

.achievement-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.achievement-desc {
    color: var(--text-light);
    font-size: 0.9rem;
}

/* Simple Statistics Section */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.stat-item {
    text-align: center;
    color: white;
}

.stat-number {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--success-green);
    margin-bottom: 1rem;
}

.stat-item h4 {
    color: white;
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.stat-item p {
    color: rgba(255,255,255,0.8);
    font-size: 1rem;
}

/* ===================================
   WHY CHOOSE US SECTION
   =================================== */

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-item {
    text-align: center;
    padding: 1.5rem;
}

.feature-icon {
    width: 80px;
    height: 80px;
    background-color: var(--light-blue);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 2rem;
    color: var(--primary-blue);
}

/* ===================================
   CONTACT PAGE STYLES
   =================================== */

.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.contact-item {
    text-align: center;
    padding: 2rem;
    background: var(--white);
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.contact-icon {
    width: 60px;
    height: 60px;
    background-color: var(--primary-blue);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: var(--white);
    font-size: 1.5rem;
}

.map-container {
    margin-top: 3rem;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.map-container iframe {
    width: 100%;
    height: 400px;
    border: none;
}

/* ===================================
   404 ERROR PAGE
   =================================== */

.error-page {
    text-align: center;
    padding: 100px 0;
}

.error-code {
    font-size: 8rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 1rem;
}

.error-message {
    font-size: 1.5rem;
    margin-bottom: 2rem;
}

/* ===================================
   RESPONSIVE DESIGN
   =================================== */

@media (max-width: 768px) {
    /* Navigation */
    nav {
        padding: 1rem 0;
        min-height: 70px;
    }

    .nav-links {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background-color: var(--white);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 2rem;
        transition: left 0.3s ease;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .nav-links.active {
        left: 0;
    }

    .menu-toggle {
        display: flex;
    }

    .menu-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .menu-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .menu-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    /* Typography */
    h1 {
        font-size: 2.5rem;
    }

    h2 {
        font-size: 2rem;
    }

    h3 {
        font-size: 1.5rem;
    }

    /* Hero Section */
    .hero {
        padding: 120px 0 80px;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    /* Page Hero Section */
    .page-hero {
        padding: 120px 0 80px;
        min-height: 50vh;
    }

    .page-hero h1 {
        font-size: 2.5rem;
    }

    .page-hero p {
        font-size: 1.1rem;
    }

    /* Form Rows */
    .form-row {
        grid-template-columns: 1fr;
    }

    /* Scroll to Top */
    .scroll-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .section-padding {
        padding: 60px 0;
    }

    h1 {
        font-size: 2rem;
    }

    .hero p {
        font-size: 1rem;
    }

    /* Page Hero Mobile */
    .page-hero {
        padding: 100px 0 60px;
        min-height: 40vh;
    }

    .page-hero h1 {
        font-size: 2rem;
    }

    .page-hero p {
        font-size: 1rem;
    }

    .page-hero-breadcrumb {
        font-size: 0.9rem;
        flex-wrap: wrap;
    }

    /* Timeline Mobile */
    .timeline::before {
        left: 20px;
    }

    .timeline-item {
        flex-direction: row !important;
        margin: 2rem 0;
    }

    .timeline-item:nth-child(even) {
        flex-direction: row !important;
    }

    .timeline-content {
        width: calc(100% - 60px);
        margin-left: 60px;
    }

    .timeline-dot {
        left: 20px;
    }

    /* Achievements Mobile */
    .achievements-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1.5rem;
    }

    .achievement-item {
        padding: 1.5rem 1rem;
    }

    .service-card,
    .testimonial-card,
    .booking-form {
        padding: 1.5rem;
    }
}
