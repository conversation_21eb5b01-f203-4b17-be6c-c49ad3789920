/* ===================================
   CleanSpace Solutions - Main JavaScript
   Interactive Features & Functionality
   =================================== */

// Wait for DOM to be fully loaded before executing scripts
document.addEventListener('DOMContentLoaded', function() {

    /* ===================================
       LOADING SCREEN
       =================================== */

    // Create and show loading screen
    const loadingScreen = document.createElement('div');
    loadingScreen.className = 'loading-screen';
    loadingScreen.innerHTML = `
        <div style="text-align: center; color: white;">
            <div class="loading-spinner"></div>
            <h3 style="margin-left: -2.5rem; margin-top: 1rem; font-weight: 300; color:#fff;">Loading...</h3>
        </div>
    `;
    document.body.appendChild(loadingScreen);

    // Hide loading screen after page loads
    window.addEventListener('load', function() {
        setTimeout(() => {
            loadingScreen.classList.add('hidden');
            setTimeout(() => {
                loadingScreen.remove();
            }, 500);
        }, 1000); // Show loading for at least 1 second
    });
    
    /* ===================================
       MOBILE NAVIGATION TOGGLE
       =================================== */
    
    // Get navigation elements
    const menuToggle = document.querySelector('.menu-toggle');
    const navLinks = document.querySelector('.nav-links');
    
    // Toggle mobile menu when hamburger is clicked
    if (menuToggle && navLinks) {
        menuToggle.addEventListener('click', function() {
            // Toggle active class on menu toggle (for animation)
            menuToggle.classList.toggle('active');
            // Toggle active class on nav links (to show/hide menu)
            navLinks.classList.toggle('active');
        });
        
        // Close mobile menu when a link is clicked
        const navLinkItems = navLinks.querySelectorAll('a');
        navLinkItems.forEach(link => {
            link.addEventListener('click', function() {
                menuToggle.classList.remove('active');
                navLinks.classList.remove('active');
            });
        });
    }
    


    /* ===================================
       SCROLL TO TOP BUTTON
       =================================== */

    // Create scroll to top button if it doesn't exist
    let scrollTopBtn = document.querySelector('.scroll-top');
    if (!scrollTopBtn) {
        scrollTopBtn = document.createElement('a');
        scrollTopBtn.href = '#';
        scrollTopBtn.className = 'scroll-top';
        scrollTopBtn.innerHTML = '↑';
        scrollTopBtn.setAttribute('aria-label', 'Scroll to top');
        document.body.appendChild(scrollTopBtn);
    }

    // Show/hide scroll to top button based on scroll position
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            scrollTopBtn.classList.add('show');
        } else {
            scrollTopBtn.classList.remove('show');
        }
    });
    
    // Smooth scroll to top when button is clicked
    scrollTopBtn.addEventListener('click', function(e) {
        e.preventDefault();
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
    
    /* ===================================
       FAQ ACCORDION FUNCTIONALITY
       =================================== */
    
    // Get all FAQ question elements
    const faqQuestions = document.querySelectorAll('.faq-question');
    
    // Add click event listener to each FAQ question
    faqQuestions.forEach(question => {
        question.addEventListener('click', function() {
            // Get the corresponding answer element
            const answer = this.nextElementSibling;
            
            // Check if this FAQ is currently active
            const isActive = this.classList.contains('active');
            
            // Close all other FAQ items
            faqQuestions.forEach(q => {
                q.classList.remove('active');
                q.nextElementSibling.classList.remove('active');
            });
            
            // If this FAQ wasn't active, open it
            if (!isActive) {
                this.classList.add('active');
                answer.classList.add('active');
            }
        });
    });
    
    /* ===================================
       FORM VALIDATION & ENHANCEMENT
       =================================== */
    
    // Get all forms on the page
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault(); // Prevent actual form submission for demo
            
            // Get all required fields in this form
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;
            
            // Validate each required field
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.style.borderColor = '#ef4444'; // Red border for invalid fields
                    
                    // Remove red border when user starts typing
                    field.addEventListener('input', function() {
                        this.style.borderColor = '';
                    });
                } else {
                    field.style.borderColor = '#10b981'; // Green border for valid fields
                }
            });
            
            // Show success or error message
            if (isValid) {
                showMessage('Thank you! Your message has been sent successfully.', 'success');
                form.reset(); // Clear the form
                
                // Reset all field border colors
                requiredFields.forEach(field => {
                    field.style.borderColor = '';
                });
            } else {
                showMessage('Please fill in all required fields.', 'error');
            }
        });
    });
    
    /* ===================================
       MESSAGE DISPLAY FUNCTION
       =================================== */
    
    // Function to show success or error messages
    function showMessage(message, type) {
        // Remove any existing message
        const existingMessage = document.querySelector('.form-message');
        if (existingMessage) {
            existingMessage.remove();
        }
        
        // Create new message element
        const messageDiv = document.createElement('div');
        messageDiv.className = `form-message ${type}`;
        messageDiv.style.cssText = `
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            text-align: center;
            font-weight: 500;
            ${type === 'success' ? 
                'background-color: #d1fae5; color: #065f46; border: 1px solid #10b981;' : 
                'background-color: #fee2e2; color: #991b1b; border: 1px solid #ef4444;'
            }
        `;
        messageDiv.textContent = message;
        
        // Insert message after the form
        const form = document.querySelector('form');
        if (form) {
            form.parentNode.insertBefore(messageDiv, form.nextSibling);
            
            // Remove message after 5 seconds
            setTimeout(() => {
                messageDiv.remove();
            }, 5000);
        }
    }
    
    /* ===================================
       SMOOTH SCROLLING FOR ANCHOR LINKS
       =================================== */
    
    // Get all anchor links that start with #
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            
            // Skip if it's just # (scroll to top button)
            if (href === '#') return;
            
            e.preventDefault();
            
            // Find the target element
            const target = document.querySelector(href);
            if (target) {
                // Calculate offset for fixed header
                const headerHeight = document.querySelector('header').offsetHeight;
                const targetPosition = target.offsetTop - headerHeight - 20;
                
                // Smooth scroll to target
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
    
    /* ===================================
       BOOKING FORM DATE RESTRICTION
       =================================== */
    
    // Set minimum date for booking form to today
    const dateInputs = document.querySelectorAll('input[type="date"]');
    const today = new Date().toISOString().split('T')[0];
    
    dateInputs.forEach(input => {
        input.setAttribute('min', today);
    });
    
    /* ===================================
       TESTIMONIALS SLIDER (if needed)
       =================================== */
    
    // Simple testimonials rotation (optional enhancement)
    const testimonialCards = document.querySelectorAll('.testimonial-card');
    if (testimonialCards.length > 3) {
        let currentTestimonial = 0;
        
        // Hide all testimonials except first 3
        testimonialCards.forEach((card, index) => {
            if (index >= 3) {
                card.style.display = 'none';
            }
        });
        
        // Rotate testimonials every 5 seconds
        setInterval(() => {
            // Hide current set
            for (let i = currentTestimonial; i < currentTestimonial + 3; i++) {
                if (testimonialCards[i]) {
                    testimonialCards[i].style.display = 'none';
                }
            }
            
            // Move to next set
            currentTestimonial = (currentTestimonial + 3) % testimonialCards.length;
            
            // Show next set
            for (let i = currentTestimonial; i < currentTestimonial + 3; i++) {
                if (testimonialCards[i]) {
                    testimonialCards[i].style.display = 'block';
                }
            }
        }, 5000);
    }
    
    /* ===================================
       LOADING ANIMATIONS
       =================================== */

    // Add fade-in animation to elements when they come into view
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';

                // Add staggered animation for service cards
                if (entry.target.classList.contains('service-card')) {
                    const cards = document.querySelectorAll('.service-card');
                    const index = Array.from(cards).indexOf(entry.target);
                    entry.target.style.transitionDelay = `${index * 0.2}s`;
                }
            }
        });
    }, observerOptions);

    // Observe elements with animation class
    const animatedElements = document.querySelectorAll('.service-card, .testimonial-card, .feature-item, .team-member');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });

    /* ===================================
       PARALLAX EFFECT FOR HERO
       =================================== */

    // Add parallax scrolling effect to hero section
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const hero = document.querySelector('.hero');
        if (hero) {
            const rate = scrolled * -0.5;
            hero.style.transform = `translateY(${rate}px)`;
        }
    });

    /* ===================================
       TYPING EFFECT FOR HERO TITLE
       =================================== */

    // Add typing effect to hero title
    const heroTitle = document.querySelector('.hero h1');
    if (heroTitle) {
        const originalText = heroTitle.textContent;
        heroTitle.textContent = '';

        setTimeout(() => {
            let i = 0;
            const typeWriter = () => {
                if (i < originalText.length) {
                    heroTitle.textContent += originalText.charAt(i);
                    i++;
                    setTimeout(typeWriter, 50);
                }
            };
            typeWriter();
        }, 1500); // Start after loading screen
    }

    /* ===================================
       COUNTER ANIMATION
       =================================== */

    // Animate counters when they come into view
    const counters = document.querySelectorAll('.counter');
    const counterObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counter = entry.target;
                const target = parseInt(counter.getAttribute('data-target'));
                const increment = target / 100;
                let current = 0;

                const updateCounter = () => {
                    if (current < target) {
                        current += increment;
                        counter.textContent = Math.ceil(current);
                        setTimeout(updateCounter, 20);
                    } else {
                        counter.textContent = target;
                    }
                };

                updateCounter();
                counterObserver.unobserve(counter);
            }
        });
    }, { threshold: 0.5 });

    counters.forEach(counter => {
        counterObserver.observe(counter);
    });
    
    /* ===================================
       CONSOLE LOG FOR DEBUGGING
       =================================== */
    
    console.log('CleanSpace Solutions - All scripts loaded successfully!');
    console.log('Template features initialized:');
    console.log('✓ Mobile navigation');
    console.log('✓ Scroll to top button');
    console.log('✓ FAQ accordion');
    console.log('✓ Form validation');
    console.log('✓ Smooth scrolling');
    console.log('✓ Loading animations');
    
});

/* ===================================
   UTILITY FUNCTIONS
   =================================== */

// Function to format phone numbers (optional enhancement)
function formatPhoneNumber(input) {
    const value = input.value.replace(/\D/g, '');
    const formattedValue = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
    input.value = formattedValue;
}

// Function to validate email format
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
