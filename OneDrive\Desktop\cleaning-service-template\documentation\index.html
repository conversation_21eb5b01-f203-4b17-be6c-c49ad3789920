<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CleanSpace Solutions Template Documentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9fafb;
        }
        .header {
            background: linear-gradient(135deg, #2563eb, #1e40af);
            color: white;
            padding: 2rem;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 2rem;
        }
        .section {
            background: white;
            padding: 2rem;
            margin-bottom: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { font-size: 2.5rem; margin-bottom: 0.5rem; }
        h2 { color: #2563eb; border-bottom: 2px solid #e5e7eb; padding-bottom: 0.5rem; }
        h3 { color: #1e40af; margin-top: 1.5rem; }
        code {
            background-color: #f3f4f6;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
        .code-block {
            background-color: #1f2937;
            color: #f9fafb;
            padding: 1rem;
            border-radius: 8px;
            overflow-x: auto;
            margin: 1rem 0;
        }
        .folder-structure {
            background-color: #f3f4f6;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 1rem 0;
        }
        .tip {
            background-color: #dbeafe;
            border-left: 4px solid #2563eb;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 8px 8px 0;
        }
        .warning {
            background-color: #fef3c7;
            border-left: 4px solid #f59e0b;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 8px 8px 0;
        }
        .success {
            background-color: #d1fae5;
            border-left: 4px solid #10b981;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 8px 8px 0;
        }
        ul, ol { margin-left: 1.5rem; }
        li { margin-bottom: 0.5rem; }
        .nav {
            background-color: white;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .nav ul {
            list-style: none;
            margin: 0;
            padding: 0;
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
        }
        .nav a {
            color: #2563eb;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        .nav a:hover {
            background-color: #dbeafe;
        }
        .screenshot-placeholder {
            background-color: #e5e7eb;
            border: 2px dashed #9ca3af;
            padding: 2rem;
            text-align: center;
            border-radius: 8px;
            margin: 1rem 0;
            color: #6b7280;
        }
    </style>
</head>
<body>
    
    <!-- Header -->
    <div class="header">
        <h1>📋 CleanSpace Solutions</h1>
        <p>Complete Template Documentation</p>
        <p style="font-size: 1.1rem; opacity: 0.9;">Professional Cleaning Service Website Template</p>
    </div>
    
    <!-- Navigation -->
    <div class="nav">
        <ul>
            <li><a href="#overview">Overview</a></li>
            <li><a href="#structure">File Structure</a></li>
            <li><a href="#editing">Editing Content</a></li>
            <li><a href="#customization">Customization</a></li>
            <li><a href="#deployment">Deployment</a></li>
            <li><a href="#support">Support</a></li>
        </ul>
    </div>
    
    <!-- Overview Section -->
    <div id="overview" class="section">
        <h2>💡 Template Overview</h2>
        
        <h3>What is CleanSpace Solutions?</h3>
        <p>CleanSpace Solutions is a modern, responsive HTML5 template designed specifically for cleaning service businesses. This template provides everything you need to create a professional online presence for your cleaning company.</p>
        
        <h3>✨ Key Features</h3>
        <ul>
            <li><strong>Fully Responsive:</strong> Works perfectly on desktop, tablet, and mobile devices</li>
            <li><strong>Modern Design:</strong> Clean, professional layout with blue and green color scheme</li>
            <li><strong>7 Complete Pages:</strong> Home, About, Services, Booking, Contact, FAQ, and 404 error page</li>
            <li><strong>Interactive Elements:</strong> Mobile menu, FAQ accordion, contact forms, and smooth scrolling</li>
            <li><strong>SEO Optimized:</strong> Proper meta tags and semantic HTML structure</li>
            <li><strong>Easy to Customize:</strong> Well-organized code with detailed comments</li>
            <li><strong>No Dependencies:</strong> Pure HTML, CSS, and JavaScript - no frameworks required</li>
        </ul>
        
        <h3>🎯 Perfect For</h3>
        <ul>
            <li>Residential cleaning services</li>
            <li>Commercial cleaning companies</li>
            <li>Office cleaning services</li>
            <li>Carpet and upholstery cleaning</li>
            <li>Move-in/move-out cleaning services</li>
            <li>Deep cleaning specialists</li>
        </ul>
        
        <div class="success">
            <strong>✅ TemplateMonster Ready:</strong> This template meets all TemplateMonster approval requirements including responsive design, clean code, and comprehensive documentation.
        </div>
    </div>
    
    <!-- File Structure Section -->
    <div id="structure" class="section">
        <h2>🗂 File Structure</h2>
        
        <p>Here's how the template files are organized:</p>
        
        <div class="folder-structure">
cleaning-service-template/
├── index.html              (Home page)
├── about.html              (About us page)
├── services.html           (Services page)
├── booking.html            (Booking form page)
├── contact.html            (Contact page)
├── faq.html               (FAQ page)
├── 404.html               (Error page)
├── README.md              (Basic instructions)
├── assets/
│   ├── css/
│   │   └── style.css      (All styles)
│   ├── js/
│   │   └── script.js      (All JavaScript)
│   ├── images/
│   │   └── hero-cleaning.jpg (Hero background image)
│   └── fonts/             (Google Fonts loaded via CSS)
└── documentation/
    └── index.html         (This documentation)
        </div>
        
        <h3>📄 Page Descriptions</h3>
        <ul>
            <li><strong>index.html:</strong> Homepage with hero section, services overview, testimonials, and call-to-action</li>
            <li><strong>about.html:</strong> Company story, values, team members, and experience statistics</li>
            <li><strong>services.html:</strong> Detailed service descriptions organized by category</li>
            <li><strong>booking.html:</strong> Comprehensive booking form with all necessary fields</li>
            <li><strong>contact.html:</strong> Contact information, contact form, and Google Maps integration</li>
            <li><strong>faq.html:</strong> Frequently asked questions with accordion functionality</li>
            <li><strong>404.html:</strong> Custom error page with helpful navigation</li>
        </ul>
        
        <h3>🎨 Asset Files</h3>
        <ul>
            <li><strong>style.css:</strong> Contains all styling including responsive design, animations, and component styles</li>
            <li><strong>script.js:</strong> Interactive functionality including mobile menu, FAQ accordion, form validation, and smooth scrolling</li>
            <li><strong>hero-cleaning.jpg:</strong> Free stock image from Pexels used as hero background</li>
        </ul>
    </div>
    
    <!-- Editing Content Section -->
    <div id="editing" class="section">
        <h2>📝 How to Edit Content</h2>
        
        <h3>✏️ Changing Text Content</h3>
        <p>All text content can be easily modified by editing the HTML files. Look for these common elements:</p>
        
        <h4>Company Name</h4>
        <p>To change "CleanSpace Solutions" to your company name:</p>
        <ol>
            <li>Open any HTML file in a text editor</li>
            <li>Use Find & Replace (Ctrl+H) to replace "CleanSpace Solutions" with your company name</li>
            <li>Repeat for all HTML files</li>
        </ol>
        
        <h4>Contact Information</h4>
        <p>Update these details in all HTML files:</p>
        <ul>
            <li><strong>Phone:</strong> Replace "(*************" with your phone number</li>
            <li><strong>Email:</strong> Replace "<EMAIL>" with your email</li>
            <li><strong>Address:</strong> Replace "123 Clean Street, City, State 12345" with your address</li>
        </ul>
        
        <h4>Service Descriptions</h4>
        <p>In <code>services.html</code>, you can:</p>
        <ul>
            <li>Modify service titles and descriptions</li>
            <li>Add new services by copying existing service card HTML</li>
            <li>Remove services by deleting the corresponding service card</li>
        </ul>
        
        <div class="tip">
            <strong>💡 Tip:</strong> Always maintain the HTML structure when editing. Only change the text content between tags, not the tags themselves.
        </div>
        
        <h3>🖼️ Changing Images</h3>
        <p>The template uses one main image:</p>
        <ul>
            <li><strong>Hero Background:</strong> <code>assets/images/hero-cleaning.jpg</code></li>
            <li>Replace this file with your own image (recommended size: 1920x1080px)</li>
            <li>Keep the same filename or update the CSS reference in <code>style.css</code></li>
        </ul>
        
        <div class="warning">
            <strong>⚠️ Image License:</strong> The included image is from Pexels and is free for commercial use. If you replace it, ensure your new image has proper licensing.
        </div>
        
        <h3>🔗 Updating Links</h3>
        <p>Social media and external links can be updated in the footer section of each HTML file:</p>
        <div class="code-block">
&lt;div class="social-icons"&gt;
    &lt;a href="YOUR_FACEBOOK_URL" aria-label="Facebook"&gt;&lt;i class="fab fa-facebook-f"&gt;&lt;/i&gt;&lt;/a&gt;
    &lt;a href="YOUR_TWITTER_URL" aria-label="Twitter"&gt;&lt;i class="fab fa-twitter"&gt;&lt;/i&gt;&lt;/a&gt;
    &lt;a href="YOUR_INSTAGRAM_URL" aria-label="Instagram"&gt;&lt;i class="fab fa-instagram"&gt;&lt;/i&gt;&lt;/a&gt;
    &lt;a href="YOUR_LINKEDIN_URL" aria-label="LinkedIn"&gt;&lt;i class="fab fa-linkedin-in"&gt;&lt;/i&gt;&lt;/a&gt;
&lt;/div&gt;
        </div>
    </div>
    
    <!-- Customization Section -->
    <div id="customization" class="section">
        <h2>🎨 Customization Guide</h2>
        
        <h3>🌈 Changing Colors</h3>
        <p>The template uses CSS custom properties (variables) for easy color customization. Edit these in <code>assets/css/style.css</code>:</p>
        
        <div class="code-block">
:root {
    --primary-blue: #2563eb;      /* Main blue color */
    --light-blue: #dbeafe;        /* Light blue backgrounds */
    --dark-blue: #1e40af;         /* Darker blue for hovers */
    --success-green: #10b981;     /* Green for CTA buttons */
    --dark-green: #059669;        /* Darker green for hovers */
    --text-dark: #1f2937;         /* Dark text color */
    --text-light: #6b7280;        /* Light text color */
    --white: #ffffff;             /* White color */
    --light-gray: #f9fafb;        /* Light gray backgrounds */
    --border-gray: #e5e7eb;       /* Border colors */
}
        </div>
        
        <h3>🔤 Changing Fonts</h3>
        <p>The template uses Google Fonts (Poppins). To change the font:</p>
        <ol>
            <li>Find a new Google Font at <a href="https://fonts.google.com" target="_blank">fonts.google.com</a></li>
            <li>Replace the import URL in <code>style.css</code></li>
            <li>Update the font-family in the body selector</li>
        </ol>
        
        <div class="code-block">
/* Replace this line */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

/* And update this */
body {
    font-family: 'YourNewFont', sans-serif;
}
        </div>
        
        <h3>➕ Adding New Services</h3>
        <p>To add a new service to the services page:</p>
        <ol>
            <li>Copy an existing service card HTML structure</li>
            <li>Update the icon (use Font Awesome icons)</li>
            <li>Change the title and description</li>
            <li>Update the link if needed</li>
        </ol>
        
        <h3>❓ Adding New FAQ Items</h3>
        <p>To add new FAQ questions:</p>
        <ol>
            <li>Copy an existing FAQ item structure in <code>faq.html</code></li>
            <li>Update the question text</li>
            <li>Update the answer content</li>
            <li>The JavaScript will automatically handle the accordion functionality</li>
        </ol>
        
        <div class="success">
            <strong>✅ Pro Tip:</strong> Always test your changes on different screen sizes to ensure responsiveness is maintained.
        </div>
    </div>
    
    <!-- Deployment Section -->
    <div id="deployment" class="section">
        <h2>🚀 Deployment Guide</h2>
        
        <h3>📤 Uploading to Web Hosting</h3>
        <ol>
            <li>Choose a web hosting provider (Bluehost, SiteGround, etc.)</li>
            <li>Upload all files to your hosting account's public_html folder</li>
            <li>Ensure <code>index.html</code> is in the root directory</li>
            <li>Test all pages and functionality</li>
        </ol>
        
        <h3>🌐 GitHub Pages (Free Hosting)</h3>
        <ol>
            <li>Create a GitHub account at <a href="https://github.com" target="_blank">github.com</a></li>
            <li>Create a new repository</li>
            <li>Upload all template files to the repository</li>
            <li>Go to Settings → Pages</li>
            <li>Select "Deploy from a branch" and choose "main"</li>
            <li>Your site will be available at: username.github.io/repository-name</li>
        </ol>
        
        <h3>⚡ Netlify (Free Hosting)</h3>
        <ol>
            <li>Go to <a href="https://netlify.com" target="_blank">netlify.com</a></li>
            <li>Drag and drop your template folder to the deploy area</li>
            <li>Your site will be live instantly with a random URL</li>
            <li>You can customize the URL in site settings</li>
        </ol>
        
        <h3>🗺️ Setting Up Google Maps</h3>
        <p>To add a real Google Map to the contact page:</p>
        <ol>
            <li>Go to <a href="https://maps.google.com" target="_blank">Google Maps</a></li>
            <li>Search for your business address</li>
            <li>Click "Share" → "Embed a map"</li>
            <li>Copy the iframe code</li>
            <li>Replace the placeholder iframe in <code>contact.html</code></li>
        </ol>
        
        <div class="tip">
            <strong>💡 SEO Tip:</strong> After deployment, submit your site to Google Search Console and add Google Analytics for tracking.
        </div>
    </div>
    
    <!-- Support Section -->
    <div id="support" class="section">
        <h2>🆘 Support & Resources</h2>
        
        <h3>📚 Learning Resources</h3>
        <ul>
            <li><strong>HTML Basics:</strong> <a href="https://www.w3schools.com/html/" target="_blank">W3Schools HTML Tutorial</a></li>
            <li><strong>CSS Basics:</strong> <a href="https://www.w3schools.com/css/" target="_blank">W3Schools CSS Tutorial</a></li>
            <li><strong>Font Awesome Icons:</strong> <a href="https://fontawesome.com/icons" target="_blank">Icon Library</a></li>
            <li><strong>Google Fonts:</strong> <a href="https://fonts.google.com" target="_blank">Font Library</a></li>
        </ul>
        
        <h3>🔧 Troubleshooting</h3>
        
        <h4>Common Issues:</h4>
        <ul>
            <li><strong>Mobile menu not working:</strong> Ensure <code>script.js</code> is properly linked</li>
            <li><strong>Styles not loading:</strong> Check that <code>style.css</code> path is correct</li>
            <li><strong>Forms not submitting:</strong> Forms are set up for demo only - connect to a backend service</li>
            <li><strong>Images not showing:</strong> Verify image file paths and names</li>
        </ul>
        
        <h3>🖼️ Image Credits</h3>
        <p>The hero background image is from Pexels and is free for commercial use:</p>
        <ul>
            <li><strong>Source:</strong> <a href="https://www.pexels.com" target="_blank">Pexels.com</a></li>
            <li><strong>License:</strong> Free for commercial use, no attribution required</li>
            <li><strong>Search terms used:</strong> "cleaning service", "professional cleaning"</li>
        </ul>
        
        <h3>📄 Template License</h3>
        <div class="success">
            <strong>✅ Commercial License:</strong> This template can be used for commercial projects. You can modify, customize, and use it for client work.
        </div>
        
        <h3>🔄 Updates & Maintenance</h3>
        <p>To keep your website running smoothly:</p>
        <ul>
            <li>Regularly update contact information</li>
            <li>Keep service descriptions current</li>
            <li>Test forms and functionality monthly</li>
            <li>Monitor site performance and loading speed</li>
            <li>Update testimonials and add new ones</li>
        </ul>
        
        <div class="tip">
            <strong>💡 Final Tip:</strong> Always backup your customized files before making major changes. This way, you can easily revert if something goes wrong.
        </div>
    </div>
    
    <!-- Footer -->
    <div style="text-align: center; padding: 2rem; color: #6b7280; border-top: 1px solid #e5e7eb; margin-top: 2rem;">
        <p><strong>CleanSpace Solutions Template</strong> - Professional Cleaning Service Website</p>
        <p>Documentation created for easy customization and deployment</p>
        <p style="font-size: 0.9rem;">© 2024 - Ready for TemplateMonster submission</p>
    </div>
    
</body>
</html>
